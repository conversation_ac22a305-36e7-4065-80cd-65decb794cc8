name: DTVM development Image Release CI

on:
  # push:
  workflow_dispatch:
    branches:
      - main
    paths:
      - 'docker/**'
permissions:
    contents: write

jobs:
  devm-dev-docker-release:
    name: Build and release DTVM development docker image on linux
    runs-on: ubuntu-latest
    steps:
      - name: Check out code
        uses: actions/checkout@v3
      # Prerequisite
      - name: Prepare Dockerfile and docker build dependencies
        run: ./docker/docker_build_x64.sh prepare
        shell: bash
      - name: Login to Docker Hub
        uses: docker/login-action@v2
        with:
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_PASSWORD }}
      - name: Extract metadata (tags, labels) for Docker
        id: meta
        uses: docker/metadata-action@v4
        with:
          images: dtvmdev1/dtvm-dev-x64
      - name: Build and push Docker image
        uses: docker/build-push-action@v3
        with:
          context: docker
          push: ${{ github.event_name != 'pull_request' }}
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
