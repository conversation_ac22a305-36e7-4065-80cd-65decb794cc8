name: DTVM evm test CI in x86-64

on:
  push:
    paths-ignore:
      - 'docs/**'
      - 'resources/**'
      - '*.md'
      - '.gitignore'
  pull_request:
    paths-ignore:
      - 'docs/**'
      - 'resources/**'
      - '*.md'
      - '.gitignore'
permissions:
    contents: read

jobs:
  build_test_interp_on_x86:
    name: Build and test DTVM-EVM interpreter on x86-64
    runs-on: ubuntu-latest
    container:
      image: dtvmdev1/dtvm-dev-x64:main
    steps:
      - name: Check out code
        uses: actions/checkout@v3
        with:
          submodules: "true"
      - name: Code Format Check
        run: |
          ./tools/format.sh check
      - name: Test Git clone
        run: |
          git clone https://github.com/asmjit/asmjit.git
      - name: Install llvm
        run: |
          echo "current home is $HOME"
          export CUR_PROJECT=$(pwd)
          cd /opt
          # ./install_llvm15.sh
          # ./install_rust.sh
          cd $CUR_PROJECT
          export LLVM_SYS_150_PREFIX=/opt/llvm15
          export LLVM_DIR=$LLVM_SYS_150_PREFIX/lib/cmake/llvm
          export PATH=$LLVM_SYS_150_PREFIX/bin:$PATH
          cd tests/wast/spec
          git apply ../spec.patch
          cd $CUR_PROJECT
          export CMAKE_BUILD_TARGET=Debug
          export ENABLE_ASAN=true
          export RUN_MODE=interpreter
          export ENABLE_LAZY=true
          export ENABLE_MULTITHREAD=true
          export TestSuite=evmtestsuite
          export CPU_EXCEPTION_TYPE='check'

          bash .ci/run_test_suite.sh
  
  build_real_test_interp_on_x86:
    name: Build and test DTVM-EVM interpreter on x86-64
    runs-on: ubuntu-latest
    container:
      image: dtvmdev1/dtvm-dev-x64:main
    steps:
      - name: Check out code
        uses: actions/checkout@v3
        with:
          submodules: "true"
      - name: Code Format Check
        run: |
          ./tools/format.sh check
      - name: Install llvm
        run: |
          echo "current home is $HOME"
          export CUR_PROJECT=$(pwd)
          cd /opt
          # ./install_llvm15.sh
          # ./install_rust.sh
          cd $CUR_PROJECT
          export LLVM_SYS_150_PREFIX=/opt/llvm15
          export LLVM_DIR=$LLVM_SYS_150_PREFIX/lib/cmake/llvm
          export PATH=$LLVM_SYS_150_PREFIX/bin:$PATH
          cd tests/wast/spec
          git apply ../spec.patch
          cd $CUR_PROJECT
          export CMAKE_BUILD_TARGET=Debug
          export ENABLE_ASAN=true
          export RUN_MODE=interpreter
          export ENABLE_LAZY=true
          export ENABLE_MULTITHREAD=true
          export TestSuite=evmrealsuite
          export CPU_EXCEPTION_TYPE='check'

          bash .ci/run_test_suite.sh

