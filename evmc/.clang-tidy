Checks: >
  bugprone-*,
  -bugprone-easily-swappable-parameters,
  -bugprone-unchecked-optional-access,
  cert-*,
  -cert-err58-cpp,
  clang-analyzer-*,
  cppcoreguidelines-*,
  -cppcoreguidelines-avoid-c-arrays,
  -cppcoreguidelines-avoid-const-or-ref-data-members,
  -cppcoreguidelines-avoid-magic-numbers,
  -cppcoreguidelines-avoid-non-const-global-variables,
  -cppcoreguidelines-non-private-member-variables-in-classes,
  -cppcoreguidelines-owning-memory,
  -cppcoreguidelines-pro-bounds-array-to-pointer-decay,
  -cppcoreguidelines-pro-bounds-constant-array-index,
  -cppcoreguidelines-pro-bounds-pointer-arithmetic,
  -cppcoreguidelines-pro-type-reinterpret-cast,
  -cppcoreguidelines-pro-type-static-cast-downcast,
  -cppcoreguidelines-pro-type-vararg,
  -cppcoreguidelines-special-member-functions,
  google-global-names-in-headers,
  google-runtime-int,
  hicpp-*,
  -hicpp-avoid-c-arrays,
  -hicpp-braces-around-statements,
  -hicpp-no-array-decay,
  -hicpp-no-malloc,
  -hicpp-signed-bitwise,
  -hicpp-special-member-functions,
  -hicpp-uppercase-literal-suffix,
  -hicpp-vararg,
  misc-*,
  -misc-non-private-member-variables-in-classes,
  modernize-*,
  -modernize-avoid-c-arrays,
  -modernize-use-nodiscard,
  -modernize-use-trailing-return-type,
  performance-*,
  portability-*,
  readability-*,
  -readability-braces-around-statements,
  -readability-else-after-return,
  -readability-function-cognitive-complexity,
  -readability-identifier-length,
  -readability-magic-numbers,
  -readability-named-parameter,
  -readability-qualified-auto,
  -readability-uppercase-literal-suffix,

WarningsAsErrors: '*'
FormatStyle:     file
