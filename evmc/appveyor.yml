version: "{build}"
image: Visual Studio 2017
branches:
  only:
  - master
  - /release\/.*/
  - appveyor
  - hunter
configuration:
  - Release
environment:
  matrix:
    - VS: 2022
      APPVEYOR_BUILD_WORKER_IMAGE: Visual Studio 2022
    - VS: 2019
      APPVEYOR_BUILD_WORKER_IMAGE: Visual Studio 2019
    - VS: 2017
    - VS: 2017-32bit

cache:
  - C:\.hunter\_Base\Cache -> cmake\Hunter\init.cmake

before_build:
  - if "%VS%" == "2022" (call "%ProgramFiles%\Microsoft Visual Studio\2022\Community\Common7\Tools\vsdevcmd" -arch=amd64)
  - if "%VS%" == "2019" (call "%ProgramFiles(x86)%\Microsoft Visual Studio\2019\Community\Common7\Tools\vsdevcmd" -arch=amd64)
  - if "%VS%" == "2017" (call "%ProgramFiles(x86)%\Microsoft Visual Studio\2017\Community\Common7\Tools\vsdevcmd" -arch=amd64)
  - if "%VS%" == "2017-32bit" (call "%ProgramFiles(x86)%\Microsoft Visual Studio\2017\Community\Common7\Tools\vsdevcmd" -arch=x86)
  - if defined VS cmake -B build -G Ninja -Wno-dev -DCMAKE_COMPILE_WARNING_AS_ERROR=TRUE -DEVMC_TESTING=ON

build_script:
  - cmake --build build --target package

after_build:
  - cd build
  - ctest -C $env:CONFIGURATION -j4 --schedule-random --output-on-failure

artifacts:
  - path: build\evmc-*.*
    name: package
