version: 2.1

orbs:
  win: circleci/windows@5.0

executors:
  linux-gcc-latest:
    docker:
      - image: ethereum/cpp-build-env:19-gcc-12
  linux-clang-latest:
    docker:
      - image: ethereum/cpp-build-env:19-clang-15


commands:
  install_doxygen:
    steps:
      - run:
          name: "Install doxygen"
          working_directory: "~"
          command: |
            export DOXYGEN_URL=https://www.doxygen.nl/files/doxygen-1.9.6.linux.bin.tar.gz
            export DOXYGEN_CHECKSUM=8354583f86416586d35397c8ee7e719f5aa5804140af83cf7ba39a8c5076bdb8
            curl $DOXYGEN_URL | tee >(tar -xz --strip-components=1) | sha256sum --check <(echo $DOXYGEN_CHECKSUM -)
            sudo ln -s $PWD/bin/doxygen /usr/bin
  build:
    parameters:
      build_type:
        type: string
        default: ""
      toolchain:
        type: string
        default: ""
    steps:
      - run:
          name: "Environment"
          command: |
            CC=${CC:-cc}
            CXX=${CXX:-cpp}
            echo CC: $CC
            echo CXX: $CXX
            $CC --version
            $CXX --version
            cmake --version

            # Create the toolchain.info file for cache key.
            echo '<<parameters.toolchain>>' >> toolchain.info
            echo '<<parameters.build_type>>' >> toolchain.info
            $CXX --version >> toolchain.info
      - restore_cache:
          name: "Restore Hunter Cache"
          key: &hunter-cache-key hunter-{{arch}}-{{checksum "toolchain.info"}}-{{checksum "cmake/Hunter/init.cmake"}}
      - run:
          name: "Configure"
          working_directory: ~/build
          command: |
            cmake ../project <<#parameters.build_type>>-DCMAKE_BUILD_TYPE=<<parameters.build_type>><</parameters.build_type>> <<#parameters.toolchain>>-DCMAKE_TOOLCHAIN_FILE=~/project/cmake/cable/toolchains/<<parameters.toolchain>>.cmake<</parameters.toolchain>> -DCMAKE_INSTALL_PREFIX=~/install -DCMAKE_COMPILE_WARNING_AS_ERROR=TRUE -DEVMC_TESTING=ON $CMAKE_OPTIONS
      - save_cache:
          name: "Save Hunter Cache"
          key: *hunter-cache-key
          paths:
            - ~/.hunter/_Base/Cache
      - run:
          name: "Build"
          command: cmake --build ~/build -- -j4
      - run:
          name: "Install"
          command: cmake --build ~/build --target install
      - run:
          name: "Package"
          command: |
            cmake --build ~/build --target package_source
            cmake --build ~/build --target package
            mkdir ~/package
            mv ~/build/evmc-*.tar.gz ~/package
      - store_artifacts:
          path: ~/package
          destination: package
      - persist_to_workspace:
          root: ~/build
          paths:
            - bin/evmc-vmtester
            - bin/evmc-example

  test:
    steps:
      - run:
          name: "Test"
          command: |
            cmake --build ~/build --target test -- ARGS="-j4 --schedule-random --output-on-failure"

  go_test:
    steps:
      - checkout
      - run:
          name: "Environment"
          command: |
            go version
            go env
            gcc --version
      - run:
          name: "Go Build"
          command: |
            go build -v ./bindings/go/evmc
            go vet -v ./bindings/go/evmc
            go generate -v ./bindings/go/evmc
            go test -v ./bindings/go/evmc
      - run:
          name: "Go module integration test"
          working_directory: test/gomod
          shell: bash -eo pipefail
          command: |
            test -z "$CIRCLE_PR_USERNAME" || { echo "Skip for PRs from forks"; exit 0; }

            V=$CIRCLE_TAG
            if [ -z $V ]; then
              V=$CIRCLE_SHA1
            fi
            echo "version: $V"

            go mod init evmc.ethereum.org/evmc_use
            go get -v $(grep -o 'github.com/ethereum/evmc/v.*' ../../go.mod)@$V
            go mod tidy -v
            go mod graph
            g++ -shared -I../../include ../../examples/example_vm/example_vm.cpp -o example-vm.so
            go test -v
            go mod graph

jobs:

  lint:
    executor: linux-clang-latest
    steps:
      - checkout
      - run:
          name: "Check code format"
          command: |
            clang-format --version
            find bindings examples include lib test tools -name '*.hpp' -o -name '*.cpp' -o -name '*.h' -o -name '*.c' | xargs clang-format -i
            git diff --color --exit-code
      - run:
          name: "Run codespell"
          command: |
            codespell --quiet-level=4 --ignore-words=./.codespell-whitelist --skip=.git
      - run:
          name: "Check bumpversion"
          command: |
            bumpversion --dry-run --verbose major
            bumpversion --dry-run --verbose minor
            bumpversion --dry-run --verbose patch
      - install_doxygen
      - run:
          name: "Test documentation"
          command: |
            cat Doxyfile | sed 's/HTML_OUTPUT            = ./HTML_OUTPUT            = ..\/docs/' | doxygen - > doxygen.log 2> doxygen.warnings
            if [ -s doxygen.warnings ]; then
              printf '\n\nDoxygen warnings:\n\n'
              cat doxygen.warnings
              exit 1
            fi
            cat doxygen.log
      - store_artifacts:
          path: ~/docs
          destination: docs

  upload-docs:
    executor: linux-clang-latest
    steps:
      - checkout
      - install_doxygen
      - run:
          name: "Generate documentation"
          command: doxygen Doxyfile
      - add_ssh_keys:
          fingerprints: [ 57:0c:50:3c:a3:72:4c:a9:28:e8:77:44:87:3b:a2:9a ]
      - run:
          name: "Upload documentation"
          command: |
            # Remove problematic symbolic link
            rm bindings/rust/evmc-sys/evmc.h
            
            git config user.name  "Documentation Bot"
            git config user.email "<EMAIL>"
            git add --all
            git commit -m "Update docs"
            git push -f origin HEAD:gh-pages

  build-clang-coverage:
    executor: linux-clang-latest
    environment:
      CMAKE_OPTIONS: -DCMAKE_CXX_CLANG_TIDY=clang-tidy -DCMAKE_C_CLANG_TIDY=clang-tidy
    steps:
      - checkout
      - build:
          build_type: Coverage
      - test
      - run:
          name: "Collect coverage data"
          working_directory: ~/build
          command: |
            find -name '*.profraw'
            llvm-profdata merge *.profraw -o evmc.profdata
            llvm-cov report -instr-profile evmc.profdata \
              -object bin/evmc-unittests \
              -object bin/evmc-vmtester
            llvm-cov export -instr-profile evmc.profdata -format=lcov > evmc.lcov \
              -object bin/evmc-unittests \
              -object bin/evmc \
              -object bin/evmc-vmtester
            genhtml evmc.lcov -o coverage -t EVMC
      - store_artifacts:
          path: ~/build/coverage
          destination: coverage
      - run:
          name: "Install codecov"
          command: |
            # TODO: This should go to cpp-build-env image
            gpg --no-default-keyring --keyring trustedkeys.gpg --keyserver keyserver.ubuntu.com --recv-key ED779869

            export CODECOV_VERSION=v0.4.1
            curl -Os https://uploader.codecov.io/$CODECOV_VERSION/linux/codecov
            curl -Os https://uploader.codecov.io/$CODECOV_VERSION/linux/codecov.SHA256SUM
            curl -Os https://uploader.codecov.io/$CODECOV_VERSION/linux/codecov.SHA256SUM.sig

            gpgv codecov.SHA256SUM.sig codecov.SHA256SUM
            shasum -c codecov.SHA256SUM

            chmod +x codecov
            sudo mv codecov /usr/local/bin
      - run:
          name: "Upload to Codecov"
          command: |
            # Convert to relative paths
            sed -i 's|$(pwd)/||' ~/build/evmc.lcov
            codecov --file ~/build/evmc.lcov -X gcov


  build-gcc-sanitizers:
    executor: linux-gcc-latest
    environment:
      CMAKE_OPTIONS: -DSANITIZE=address,undefined
    steps:
      - checkout
      - build
      - test

  build-clang-sanitizers:
    executor: linux-clang-latest
    environment:
      CMAKE_OPTIONS: -DSANITIZE=address,undefined,nullability,implicit-unsigned-integer-truncation,implicit-signed-integer-truncation
      UBSAN_OPTIONS: halt_on_error=1
    steps:
      - checkout
      - build
      - test

  build-clang9-asan:
    docker:
      - image: ethereum/cpp-build-env:15-clang-9
    environment:
      CMAKE_OPTIONS: -DSANITIZE=address
      UBSAN_OPTIONS: halt_on_error=1
    steps:
      - checkout
      - build
      - test

  build-gcc-min:
    docker:
      - image: ethereum/cpp-build-env:15-gcc-8
    steps:
      - checkout
      - build
      - test

  build-clang-min:
    docker:
      - image: ethereum/cpp-build-env:15-clang-9
    steps:
      - checkout
      - build
      - test

  build-gcc-32bit:
    docker:
      - image: ethereum/cpp-build-env:17-gcc-11-multilib
    steps:
      - checkout
      - build:
          toolchain: cxx11-32bit
      - test

  build-cmake-min:
    docker:
      - image: cimg/base:stable-20.04
    steps:
      - run:
          name: "Install default CMake"
          command: sudo apt -q update && sudo apt -qy install cmake
      - checkout
      - build
      - test

  build-macos:
    resource_class: macos.m1.medium.gen1
    macos:
      xcode: 14.3.1
    steps:
      - run:
          name: "Install System Dependencies"
          command: HOMEBREW_NO_AUTO_UPDATE=1 brew install cmake
      - checkout
      - build
      - test

  build-windows:
    executor: win/server-2022
    environment:
      CMAKE_BUILD_TYPE: Release
    steps:
      - checkout
      - run:
          name: "Setup environment (bash)"
          shell: bash
          command: |
            echo 'export PATH=$PATH:"/c/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/bin"' >> $BASH_ENV
      - run:
          name: 'Configure'
          shell: powershell
          command: |
            $ErrorActionPreference = "Stop"
            & 'C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\Tools\Launch-VsDevShell.ps1' -Arch amd64
            which cmake
            cmake -S . -B ~/build -G Ninja -DCMAKE_COMPILE_WARNING_AS_ERROR=TRUE -DCMAKE_INSTALL_PREFIX=C:\install -DEVMC_TESTING=ON
      - run:
          name: 'Build'
          shell: powershell
          command: |
            $ErrorActionPreference = "Stop"
            & 'C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\Tools\Launch-VsDevShell.ps1' -Arch amd64
            cmake --build ~/build
      - run:
          name: 'Test'
          shell: powershell
          command: |
            $ErrorActionPreference = "Stop"
            & 'C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\Tools\Launch-VsDevShell.ps1' -Arch amd64
            cmake --build ~/build --target test

  build-windows-32bit:
    executor: win/server-2022
    environment:
      CMAKE_BUILD_TYPE: Release
    steps:
      - checkout
      - run:
          name: "Setup environment (bash)"
          shell: bash
          command: |
            echo 'export PATH=$PATH:"/c/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/bin"' >> $BASH_ENV
      - run:
          name: 'Configure'
          shell: powershell
          command: |
            $ErrorActionPreference = "Stop"
            & 'C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\Tools\Launch-VsDevShell.ps1' -Arch x86
            which cmake
            cmake -S . -B ~/build -G Ninja -DCMAKE_COMPILE_WARNING_AS_ERROR=TRUE -DCMAKE_INSTALL_PREFIX=C:\install -DEVMC_TESTING=ON
      - run:
          name: 'Build'
          shell: powershell
          command: |
            $ErrorActionPreference = "Stop"
            & 'C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\Tools\Launch-VsDevShell.ps1' -Arch x86
            cmake --build ~/build
      - run:
          name: 'Test'
          shell: powershell
          command: |
            $ErrorActionPreference = "Stop"
            & 'C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\Tools\Launch-VsDevShell.ps1' -Arch x86
            cmake --build ~/build --target test

  bindings-go-latest:
    docker:
      - image: cimg/go:1.20
    steps:
      - go_test

  bindings-go-min:
    docker:
      - image: circleci/golang:1.11
    steps:
      - go_test

  bindings-go-windows:
    executor: win/server-2022
    steps:
      - run:
          name: "Install mingw"
          command: |
            # Version 12.2 (latest when we pinned 11.2) was failing.
            # TODO: remove the pin in the future
            choco install -y mingw --version 11.2
      - go_test

  bindings-rust:
    docker:
      - image: rust:1
    steps:
      - checkout
      - run:
          name: Update environment
          command: |
            apt -qq update
            apt -yq install libclang-dev clang --no-install-recommends
            rustup component add rustfmt
      - run:
          name: Check formatting
          command: |
            rustfmt --version
            cargo fmt --all -- --check
      - run:
          name: Build
          command: cargo build
      - run:
          name: Test
          command: cargo test
      - attach_workspace:
          at: ~/build
      - run:
          name: Test with evmc-vmtester
          command: |
            ~/build/bin/evmc-vmtester target/debug/libexamplerustvm.so
      - run:
          name: Test with evmc-example
          command: |
            ~/build/bin/evmc-example target/debug/libexamplerustvm.so
      - run:
          name: Package evmc-sys
          # NOTE: can not be run for evmc-vm and evmc-declare due to version dependencies
          working_directory: bindings/rust/evmc-sys
          command: cargo package

  bindings-rust-asan:
    docker:
      - image: rust:1-bullseye
    steps:
      - checkout
      - run:
          name: Update environment
          command: |
            apt -qq update
            apt -yq install llvm-11-dev clang-11 --no-install-recommends
            rustup toolchain install nightly-x86_64-unknown-linux-gnu
      - run:
          name: Build
          command: RUSTFLAGS="-Z sanitizer=address" ASAN_OPTIONS=detect_leaks=1 cargo +nightly build --target x86_64-unknown-linux-gnu
      - run:
          name: Test
          command: RUSTFLAGS="-Z sanitizer=address -C opt-level=0" ASAN_OPTIONS=detect_leaks=1 cargo +nightly test --target x86_64-unknown-linux-gnu
      - attach_workspace:
          at: ~/build
      - run:
          name: Test with evmc-vmtester
          command: |
            ~/build/bin/evmc-vmtester target/x86_64-unknown-linux-gnu/debug/libexamplerustvm.so
      - run:
          name: Test with evmc-example
          command: |
            ~/build/bin/evmc-example target/x86_64-unknown-linux-gnu/debug/libexamplerustvm.so

workflows:
  version: 2
  evmc:
    jobs:
      - lint
      - build-clang-coverage
      - build-gcc-sanitizers
      - build-clang-sanitizers
      - build-clang9-asan
      - build-gcc-min
      - build-clang-min
      - build-gcc-32bit
      - build-cmake-min
      - build-macos
      - build-windows
      - build-windows-32bit
      - bindings-go-latest:
          filters:
            tags:
              only: /.*/
      - bindings-go-min:
          filters:
            tags:
              only: /.*/
      - bindings-go-windows
      - bindings-rust:
          requires:
            - build-gcc-min
      - bindings-rust-asan:
          requires:
            - build-clang9-asan
      - upload-docs:
          requires:
            - lint
          filters:
            branches:
              only:
                - master
