branches:
  only:
    - master
    - /^release\/.*$/
    - travis

matrix:
  include:
    - language: cpp
      os: windows

cache:
  directories:
    - C:/Users/<USER>/.hunter/_Base/Cache

install:
  - cmake --version
  - mkdir build && cd build
  - cmake .. -G "Visual Studio 15 2017 Win64" -DEVMC_TESTING=ON -DCMAKE_INSTALL_PREFIX=$HOME
  - cmake --build . --config Release --target install

script:
  - ctest -C Release -j4 --schedule-random --output-on-failure
