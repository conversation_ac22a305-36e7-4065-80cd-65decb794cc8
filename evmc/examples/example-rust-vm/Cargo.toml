# EVMC: Ethereum Client-VM Connector API.
# Copyright 2019 The EVMC Authors.
# Licensed under the Apache License, Version 2.0.

[package]
name = "example-rust-vm"
version = "12.1.0"
authors = ["<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>"]
edition = "2018"
publish = false

[lib]
name = "examplerustvm"
crate-type = ["staticlib", "cdylib"]

[dependencies]
evmc-sys = { path = "../../bindings/rust/evmc-sys" }
evmc-vm = { path = "../../bindings/rust/evmc-vm" }
evmc-declare = { path = "../../bindings/rust/evmc-declare" }
