# EVMC: Ethereum Client-VM Connector API.
# Copyright 2019 The EVMC Authors.
# Licensed under the Apache License, Version 2.0.

[package]
name = "evmc-declare-tests"
version = "12.1.0"
authors = ["<PERSON> <<EMAIL>>"]
license = "Apache-2.0"
repository = "https://github.com/ethereum/evmc"
description = "Bindings to EVMC (VM declare macro) -- Test crate"
edition = "2018"
publish = false

[dependencies]
evmc-declare = { path = "../evmc-declare" }
evmc-sys = { path = "../evmc-sys" }
evmc-vm = { path = "../evmc-vm" }
