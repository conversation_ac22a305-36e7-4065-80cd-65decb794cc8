# EVMC: Ethereum Client-VM Connector API.
# Copyright 2019 The EVMC Authors.
# Licensed under the Apache License, Version 2.0.

[package]
name = "evmc-declare"
version = "12.1.0"
authors = ["<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>"]
license = "Apache-2.0"
repository = "https://github.com/ethereum/evmc"
description = "Bindings to EVMC (VM declare macro)"
edition = "2018"

[dependencies]
quote = "1.0"
heck = "0.3.1"
proc-macro2 = "1.0"
syn = { version = "1.0", features = ["full"] }
# For documentation examples
evmc-vm = { path = "../evmc-vm", version = "12.1.0" }

[lib]
proc-macro = true
