[bumpversion]
current_version = 12.1.0
tag = True
sign_tags = True
tag_message = EVMC {new_version}
commit = True
message = EVMC {new_version}
	
	Bump version: {current_version} → {new_version}
parse = (?P<major>\d+)\.(?P<minor>\d+)\.(?P<patch>\d+)(-(?P<prerel>rc|alpha)\.(?P<prerelver>\d+))?
serialize = 
	{major}.{minor}.{patch}-{prerel}.{prerelver}
	{major}.{minor}.{patch}

[bumpversion:part:prerel]
optional_value = rel
values = 
	alpha
	rc
	rel

[bumpversion:file:CMakeLists.txt]

[bumpversion:file:include/evmc/evmc.h]
serialize = {major}
search = EVMC_ABI_VERSION = {current_version}
replace = EVMC_ABI_VERSION = {new_version}

[bumpversion:file:go.mod]
serialize = {major}
search = github.com/ethereum/evmc/v{current_version}
replace = github.com/ethereum/evmc/v{new_version}

[bumpversion:file:test/gomod/use_evmc_test.go]
serialize = {major}
search = github.com/ethereum/evmc/v{current_version}
replace = github.com/ethereum/evmc/v{new_version}

[bumpversion:file:test/gomod/README]
serialize = {major}
search = github.com/ethereum/evmc/v{current_version}
replace = github.com/ethereum/evmc/v{new_version}

[bumpversion:file:bindings/rust/evmc-sys/Cargo.toml]
search = version = "{current_version}"
replace = version = "{new_version}"

[bumpversion:file:bindings/rust/evmc-vm/Cargo.toml]
search = version = "{current_version}"
replace = version = "{new_version}"

[bumpversion:file:bindings/rust/evmc-declare/Cargo.toml]
search = version = "{current_version}"
replace = version = "{new_version}"

[bumpversion:file:bindings/rust/evmc-declare-tests/Cargo.toml]
search = version = "{current_version}"
replace = version = "{new_version}"

[bumpversion:file:examples/example-rust-vm/Cargo.toml]
search = version = "{current_version}"
replace = version = "{new_version}"

[bumpversion:file:examples/example-rust-vm/src/lib.rs]
search = "{current_version}"
replace = "{new_version}"

[bumpversion:file:docs/EVMC.md]
serialize = {major}
search = ABI version {current_version}
replace = ABI version {new_version}
