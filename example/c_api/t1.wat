(module
  (import "env" "print_i32" (func $print_i32 (param i32)))
  (import "env" "print_i32_f32" (func $print_i32_f32 (param i32 f32)))
  (import "env" "print_f64_f64" (func $print_f64_f64 (param f64 f64)))
  (import "env" "print_f32" (func $print_f32 (param f32)))
  (import "env" "print_f64" (func $print_f64 (param f64)))
  (import "env" "print_str" (func $print_str (param i32)))
  (memory 1)
  (func $entry (result i32)
    (call $print_i32 (i32.const 1040))
    (call $print_i32_f32 (i32.const 4829) (f32.const 164.2))
    (call $print_f64_f64 (f64.const 7621340.43) (f64.const 47483.34))
    (call $print_f32 (f32.const 23123.14))
    (call $print_f64 (f64.const 19384.44))
    (call $print_str (i32.const 0))
    (call $print_str (i32.const 20))
    (call $print_str (i32.const 40))
    (i32.const 0)
  )
  (data (i32.const 0) "Hello, World!\n\00")
  (data (i32.const 20) "Hello, World!\n\00")
  (data (i32.const 40) "ZetaEngine is the best WebAssembly Virtual Machine!\n\00")
  (export "entry" (func $entry))
)